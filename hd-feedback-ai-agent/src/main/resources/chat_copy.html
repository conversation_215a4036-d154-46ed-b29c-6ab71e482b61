<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI对话界面</title>
  <script src="https://cdn.jsdelivr.net/npm/react@18/umd/react.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    }
    textarea:focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
    .message-content {
      text-align: left;
    }
    .markdown-content p {
      margin-bottom: 1.25rem
    }
    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }
    .markdown-content li {
      margin-bottom: 0.5rem;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content code {
      background-color: #1f2937;
      padding: 0.1rem 0.3rem;
      border-radius: 0.2rem;
    }
    .markdown-content pre {
      background-color: #1f2937;
      padding: 0.75rem;
      border-radius: 0.3rem;
      overflow-x: auto;
    }
  </style>
</head>
<body class="bg-black text-gray-100">
<div id="root"></div>
<script type="text/babel">
  const { useState, useEffect, useRef } = React;
  // 配置 marked 以支持单个换行和更宽松的 Markdown 解析
  marked.setOptions({
    breaks: true, // 单个换行符视为 <br>
    gfm: true, // 启用 GitHub Flavored Markdown
  });

  const tools = [
    { id: 'cloudgame', name: '云游戏' },
    { id: 'gift', name: '送礼' },
    { id: 'calculator', name: '计算器' },
    { id: 'code', name: '代码解释器' },
  ];

  const newChat = {
    id: Date.now().toString(),
    title: `默认聊天`,
    messages: []
  };

  function App() {
    const [chats, setChats] = useState([]);
    const [currentChat, setCurrentChat] = useState(newChat);
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [selectedTool, setSelectedTool] = useState('feedback');
    const [isStreaming, setIsStreaming] = useState(false);
    const messagesEndRef = useRef(null);

    useEffect(() => {
      const savedChats = JSON.parse(localStorage.getItem('chats') || '[]');
      setChats(savedChats);
      if (savedChats.length > 0) {
        setCurrentChat(savedChats[0].id);
        setMessages(savedChats[0].messages);
      }
    }, []);

    useEffect(() => {
      localStorage.setItem('chats', JSON.stringify(chats));
    }, [chats]);

    useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    const handleNewChat = () => {
      const newChat = {
        id: Date.now().toString(),
        title: `聊天 ${chats.length + 1}`,
        messages: [],
      };
      setChats([newChat, ...chats]);
      setCurrentChat(newChat.id);
      setMessages([]);
    };

    const handleSelectChat = (chatId) => {
      const chat = chats.find(c => c.id === chatId);
      setCurrentChat(chatId);
      setMessages(chat ? chat.messages : []);
    };

    const handleSendMessage = async (e) => {
      e.preventDefault();
      if (!input.trim() || isStreaming) return;

      const newMessage = {
        id: Date.now(),
        content: input,
        role: 'user',
        tool: selectedTool,
      };

      const updatedMessages = [...messages, newMessage];
      setMessages(updatedMessages);
      setInput('');
      setChats(chats.map(chat =>
              chat.id === currentChat ? { ...chat, messages: updatedMessages } : chat
      ));

      setIsStreaming(true);
      const aiMessage = {
        id: Date.now() + 1,
        content: '',
        role: 'assistant',
      };
      setMessages([...updatedMessages, aiMessage]);

      try {
        const response = await fetch('http://localhost:8080/stream/' + selectedTool, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          body: JSON.stringify({ chatId: currentChat, message: input, tool: selectedTool }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            if (buffer.trim()) {
              console.warn('Remaining buffer:', buffer);
            }
            setIsStreaming(false);
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n\n');
          buffer = lines.pop(); // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data:')) {
              let token = line.slice(5);
              while (token.endsWith('data:')) {
                token = token.replace(/data:?/g, function(match) {
                  // 如果匹配的data:后面有换行，则只删除data:保留换行
                  return match.endsWith('data:') ? '' : '';
                });
              }
              if (token === '[DONE]') {
                setIsStreaming(false);
                break;
              }
              if (token) {
                setMessages(prev => {
                  const newMessages = [...prev];
                  newMessages[newMessages.length - 1] = {
                    ...newMessages[newMessages.length - 1],
                    content: newMessages[newMessages.length - 1].content + token,
                  };
                  setChats(chats.map(chat =>
                          chat.id === currentChat ? { ...chat, messages: newMessages } : chat
                  ));
                  return newMessages;
                });
              }
            }
          }
        }
      } catch (error) {
        console.error('Stream error:', error);
        setMessages(prev => {
          const newMessages = [...prev];
          newMessages[newMessages.length - 1] = {
            ...newMessages[newMessages.length - 1],
            content: newMessages[newMessages.length - 1].content + '\n[错误: 无法接收流式响应]',
          };
          return newMessages;
        });
        setIsStreaming(false);
      }
    };

    return (
            <div className="flex h-screen">
              {/* 侧边栏 */}
              <div className="w-72 bg-black border-r border-gray-800 p-6 flex flex-col">
                <button
                        onClick={handleNewChat}
                        className="mb-6 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                >
                  新建聊天
                </button>
                <div className="flex-1 overflow-y-auto space-y-2">
                  {chats.map(chat => (
                          <div
                                  key={chat.id}
                                  onClick={() => handleSelectChat(chat.id)}
                                  className={`py-2 px-4 rounded-lg cursor-pointer text-sm ${
                                          currentChat === chat.id ? 'bg-gray-800 text-blue-400' : 'hover:bg-gray-900 text-gray-400'
                                  } transition-colors`}
                          >
                            {chat.title}
                          </div>
                  ))}
                </div>
              </div>

              {/* 主聊天区域 */}
              <div className="flex-1 flex flex-col">
                <div className="flex-1 overflow-y-auto p-8">
                  {messages.length === 0 && (
                          <div className="flex items-center justify-center h-full text-gray-500 text-lg">
                            开始新的对话吧！
                          </div>
                  )}
                  {messages.map(message => (
                          <div
                                  key={message.id}
                                  className={`mb-6 max-w-3xl mx-auto ${
                                          message.role === 'user' ? 'text-right' : 'text-left'
                                  }`}
                          >
                            <div
                                    className={`inline-block p-4 rounded-xl text-sm markdown-content message-content ${
                                            message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-800 text-gray-200'
                                    }`}
                            >
                              {message.role === 'assistant' ? (
                                      <div dangerouslySetInnerHTML={{ __html: marked.parse(message.content) }} />
                              ) : (
                                      message.content
                              )}
                              {message.role === 'user' && (
                                      <div className="text-xs text-gray-300 mt-2">
                                        工具: {tools.find(t => t.id === message.tool)?.name}
                                      </div>
                              )}
                            </div>
                          </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
                <div className="p-6 bg-black">
                  <form onSubmit={handleSendMessage} className="max-w-3xl mx-auto">
                    <div className="flex items-start bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
                      <div className="flex items-center">
                        <select
                                value={selectedTool}
                                onChange={(e) => setSelectedTool(e.target.value)}
                                className="bg-gray-800 text-gray-200 p-3 pl-4 pr-8 text-sm border-r border-gray-700 focus:outline-none h-[48px]"
                                disabled={isStreaming}
                        >
                          {tools.map(tool => (
                                  <option key={tool.id} value={tool.id}>{tool.name}</option>
                          ))}
                        </select>
                      </div>
                      <textarea
                              value={input}
                              onChange={(e) => setInput(e.target.value)}
                              className="flex-1 bg-gray-900 text-gray-100 p-3 text-base focus:outline-none resize-y min-h-[120px] placeholder-gray-500"
                              placeholder="输入您的问题..."
                              rows="4"
                              disabled={isStreaming}
                      />
                      <button
                              type="submit"
                              className={`bg-blue-500 text-white font-medium px-6 h-[48px] ${
                                      isStreaming ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'
                              } transition-colors`}
                              disabled={isStreaming}
                      >
                        发送
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
    );
  }

  ReactDOM.render(<App />, document.getElementById('root'));
</script>
</body>
</html>