app:
  id: # jvm參數
apollo:
  meta: # jvm參數
  cluster: # jvm參數
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true

#shenshu:
#  #  enabled: true #默认开启，关闭设置为false
#  udb:
#    appid: 9999
#    app-key: 2C7B9D99572FAC91CB62352CAEE4F05A
#    auto-login: true #默认开启
#    index-uri: /chat.html #登录成功后跳转的页面，需要自己实现
#    login-uri: /login.html #登录页面的uri，内置已经实现了，此处只是配置它的展示uri，未登录时请求需要登录的接口会被重定向到此uri
#    cookie-domain:
#      - lgn.yy.com
#    anon-path: #无需登录的uri，如果没有其他配置/**，则默认/**都需要登录
#      - /**/public/**
#      - /**/*.png
#      - /**/*.css
#      - /error
#shiro:
#  filterChainDefinitions: /**=anon
#  unauthorizedUrl: /unauthorized
#  loginUrl: /login.html
#  successUrl: /chat.html