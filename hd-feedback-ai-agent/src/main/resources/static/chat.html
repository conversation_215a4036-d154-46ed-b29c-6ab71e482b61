<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能客服助手</title>
  <script src="https://cdn.jsdelivr.net/npm/react@18/umd/react.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
      background-color: #0f0f0f;
    }
    textarea:focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
    .message-content {
      text-align: left;
    }
    .markdown-content p {
      margin-bottom: 1.25rem
    }
    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }
    .markdown-content li {
      margin-bottom: 0.5rem;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content code {
      background-color: #1f2937;
      padding: 0.1rem 0.3rem;
      border-radius: 0.2rem;
      font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    }
    .markdown-content pre {
      background-color: #1f2937;
      padding: 0.75rem;
      border-radius: 0.3rem;
      overflow-x: auto;
      margin: 1rem 0;
    }
    .markdown-content pre code {
      background-color: transparent;
      padding: 0;
    }
    /* Grok风格的消息气泡 */
    .message-bubble {
      max-width: 90%;
      border-radius: 1.25rem;
      padding: 1rem 1.25rem;
      margin-bottom: 1.5rem;
      line-height: 1.5;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .user-message {
      background-color: #1a1a1a;
      color: #ffffff;
      border: 1px solid #2a2a2a;
      align-self: flex-end;
    }
    
    .assistant-message {
      background-color: #1a1a1a;
      color: #ffffff;
      border: 1px solid #2a2a2a;
      align-self: flex-start;
    }
    
    /* Grok风格的侧边栏 */
    .sidebar {
      background-color: #0f0f0f;
      border-right: 1px solid #2a2a2a;
    }
    
    .chat-item {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    
    .chat-item:hover {
      background-color: #1a1a1a;
    }
    
    .chat-item.active {
      background-color: #1a1a1a;
      border-left: 3px solid #3b82f6;
    }
    
    /* Grok风格的输入框 */
    .input-container {
      background-color: #1a1a1a;
      border: 1px solid #2a2a2a;
      border-radius: 1rem;
      transition: all 0.2s ease;
    }
    
    .input-container:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    
    .typing-indicator {
      display: inline-flex;
      align-items: center;
    }
    .typing-indicator span {
      height: 8px;
      width: 8px;
      margin: 0 1px;
      background-color: #a3a3a3;
      border-radius: 50%;
      display: inline-block;
      animation: bounce 1.4s infinite ease-in-out both;
    }
    .typing-indicator span:nth-child(1) {
      animation-delay: -0.32s;
    }
    .typing-indicator span:nth-child(2) {
      animation-delay: -0.16s;
    }
    @keyframes bounce {
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1); }
    }
    .input-container {
      border: 1px solid #374151;
      transition: all 0.2s ease;
    }
    .input-container:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    .send-btn {
      transition: all 0.2s ease;
    }
    .send-btn:hover {
      transform: translateY(-1px);
    }
    .send-btn:active {
      transform: translateY(1px);
    }
  </style>
</head>
<body class="bg-black text-gray-100">
<div id="root"></div>
<script type="text/babel">
  const { useState, useEffect, useRef } = React;
  // 配置 marked 以支持单个换行和更宽松的 Markdown 解析
  marked.setOptions({
    breaks: true, // 单个换行符视为 <br>
    gfm: true, // 启用 GitHub Flavored Markdown
    highlight: function(code, lang) {
      return code;
    }
  });

  const createNewChat = (title = '新对话') => ({
    id: Date.now().toString(),
    title,
    messages: [],
    createdAt: new Date().toISOString()
  });

  function App() {
    const [chats, setChats] = useState([]);
    const [currentChat, setCurrentChat] = useState(null);
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [categories, setCategories] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState();
    const [isStreaming, setIsStreaming] = useState(false);
    const [controller, setController] = useState(null);
    const messagesEndRef = useRef(null);
    const textareaRef = useRef(null);

    // 添加获取categories的函数
    const fetchCategories = async () => {
      try {
        const response = await fetch('http://local.yy.com/api/categories', {
          method: 'GET', // 明确指定方法
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            // 浏览器会自动添加Origin头，不需要手动添加
          },
        });
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
          if (data.length > 0) {
            setSelectedCategory(data[0].id);
          }
        } else {
          console.error('Failed to fetch categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    useEffect(() => {
      fetchCategories();
      const savedChats = JSON.parse(localStorage.getItem('chats') || '[]');
      setChats(savedChats);
      if (savedChats.length > 0) {
        setCurrentChat(savedChats[0].id);
        setMessages(savedChats[0].messages);
      }
    }, []);

    useEffect(() => {
      localStorage.setItem('chats', JSON.stringify(chats));
    }, [chats]);

    useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    const handleNewChat = () => {
      const newChatObj = createNewChat();
      setChats([newChatObj, ...chats]);
      setCurrentChat(newChatObj.id);
      setMessages([]);
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    };

    const handleSelectChat = (chatId) => {
      const chat = chats.find(c => c.id === chatId);
      setCurrentChat(chatId);
      setMessages(chat ? chat.messages : []);
    };

    const handleDeleteChat = (e, chatId) => {
      e.stopPropagation();
      const updatedChats = chats.filter(chat => chat.id !== chatId);
      setChats(updatedChats);
      
      if (currentChat === chatId) {
        if (updatedChats.length > 0) {
          setCurrentChat(updatedChats[0].id);
          setMessages(updatedChats[0].messages);
        } else {
          setCurrentChat(null);
          setMessages([]);
        }
      }
    };

    const stopStreaming = () => {
      if (controller) {
        controller.abort();
        setController(null);
        setIsStreaming(false);
      }
    };

    const handleKeyDown = (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSendMessage(e);
      }
    };

    const handleSendMessage = async (e) => {
      e.preventDefault();
      if (!input.trim() || isStreaming) return;

      // 如果是第一次发消息，创建新对话
      let chatId = currentChat;
      let isNewChat = false;
      
      if (!chatId || chats.length === 0) {
        const newChatObj = createNewChat('新对话'); // 先用默认标题
        setChats([newChatObj, ...chats]);
        setCurrentChat(newChatObj.id);
        chatId = newChatObj.id;
        isNewChat = true;
      }

      const newMessage = {
        id: Date.now(),
        content: input,
        role: 'user',
        category: selectedCategory,
        timestamp: new Date().toISOString()
      };

      const updatedMessages = [...messages, newMessage];
      setMessages(updatedMessages);
      setInput('');
      
      // 更新聊天列表中的消息，如果是新对话则同时更新标题
      setChats(prevChats => {
        return prevChats.map(chat => {
          if (chat.id === chatId) {
            // 如果是新对话，更新标题为消息内容的前15个字符
            if (isNewChat || chat.messages.length === 0) {
              const title = input.length > 15 ? input.substring(0, 15) + '...' : input;
              return { ...chat, title: title, messages: updatedMessages };
            }
            return { ...chat, messages: updatedMessages };
          }
          return chat;
        });
      });

      setIsStreaming(true);
      const aiMessage = {
        id: Date.now() + 1,
        content: '',
        role: 'assistant',
        timestamp: new Date().toISOString()
      };
      
      // 添加AI消息到当前消息列表
      const messagesWithAiResponse = [...updatedMessages, aiMessage];
      setMessages(messagesWithAiResponse);
      
      // 同时更新聊天列表中的消息
      setChats(prevChats => {
        return prevChats.map(chat =>
          chat.id === chatId ? { ...chat, messages: messagesWithAiResponse } : chat
        );
      });

      try {
        const abortController = new AbortController();
        setController(abortController);
        
        // 使用正确的API端点
        const response = await fetch('http://local.yy.com/api/stream/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            // 浏览器会自动添加Origin头，不需要手动添加
          },
          credentials: 'include', // 添加这一行，确保请求携带Cookie
          body: JSON.stringify({ chatId: chatId, message: input, category: selectedCategory }),
          signal: abortController.signal
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            if (buffer.trim()) {
              console.warn('Remaining buffer:', buffer);
            }
            setIsStreaming(false);
            setController(null);
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n\n');
          buffer = lines.pop(); // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.startsWith('data:')) {
              let token = line.slice(5);
              while (token.endsWith('data:')) {
                token = token.replace(/data:?/g, function(match) {
                  // 如果匹配的data:后面有换行，则只删除data:保留换行
                  return match.endsWith('data:') ? '' : '';
                });
              }
              if (token === '[DONE]') {
                setIsStreaming(false);
                setController(null);
                break;
              }
              if (token) {
                setMessages(prev => {
                  const newMessages = [...prev];
                  newMessages[newMessages.length - 1] = {
                    ...newMessages[newMessages.length - 1],
                    content: newMessages[newMessages.length - 1].content + token,
                  };
                  setChats(prevChats => prevChats.map(chat =>
                          chat.id === chatId ? { ...chat, messages: newMessages } : chat
                  ));
                  return newMessages;
                });
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Stream error:', error);
          setMessages(prev => {
            const newMessages = [...prev];
            newMessages[newMessages.length - 1] = {
              ...newMessages[newMessages.length - 1],
              content: newMessages[newMessages.length - 1].content + '\n[错误: 无法接收流式响应]',
            };
            return newMessages;
          });
        }
        setIsStreaming(false);
        setController(null);
      }
    };

    const formatTime = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    return (
      <div className="flex h-screen bg-black">
        {/* 侧边栏 - Grok风格 */}
        <div className="w-72 sidebar p-4 flex flex-col">
          <button
            onClick={handleNewChat}
            className="mb-6 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 text-sm flex items-center justify-center shadow-md hover:shadow-lg"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            新建对话
          </button>
          <div className="flex-1 overflow-y-auto space-y-1.5 pr-1">
            {chats.length === 0 ? (
              <div className="text-gray-500 text-sm text-center mt-8">
                没有对话记录
              </div>
            ) : (
              chats.map(chat => (
                <div
                  key={chat.id}
                  onClick={() => handleSelectChat(chat.id)}
                  className={`py-2.5 px-3 rounded-lg cursor-pointer text-sm flex justify-between items-center chat-item ${
                    currentChat === chat.id ? 'active text-blue-400 pl-2' : 'hover:bg-gray-800/50 text-gray-300'
                  } transition-all duration-200`}
                >
                  <div className="flex items-center truncate flex-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                    <span className="truncate">{chat.title}</span>
                  </div>
                  <button 
                    onClick={(e) => handleDeleteChat(e, chat.id)}
                    className="ml-1 text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-700/50 delete-btn"
                    title="删除对话"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 主聊天区域 - Grok风格 */}
        <div className="flex-1 flex flex-col bg-black">
          <div className="flex-1 overflow-y-auto p-4 md:p-6">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-xl font-medium mb-2">开始新的对话</p>
                <p className="text-sm text-gray-500 max-w-md text-center">
                  选择一个工具，输入您的问题，开始与智能客服助手对话
                </p>
              </div>
            ) : (
              <div className="max-w-3xl mx-auto">
                {messages.map((message, index) => (
                  <div
                    key={message.id}
                    className={`flex flex-col mb-6 ${message.role === 'user' ? 'items-end' : 'items-start'}`}
                  >
                    <div className={`flex items-center mb-2 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.role === 'user' ? 'ml-2 bg-blue-600' : 'mr-2 bg-purple-600'
                      }`}>
                        {message.role === 'user' ? '👤' : '🤖'}
                      </div>
                      <div className="text-sm font-medium">
                        {message.role === 'user' ? '你' : '智能客服助手'}
                      </div>
                      <div className={`text-xs text-gray-500 ${message.role === 'user' ? 'mr-2' : 'ml-2'}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                    <div 
                      className={`message-bubble ${
                        message.role === 'user' ? 'user-message mr-10' : 'assistant-message ml-10'
                      }`}
                    >
                      {message.role === 'assistant' && message.content === '' && isStreaming ? (
                        <div className="typing-indicator">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      ) : message.role === 'assistant' ? (
                        <div className="markdown-content" dangerouslySetInnerHTML={{ __html: marked.parse(message.content) }} />
                      ) : (
                        <div>
                          <div>{message.content}</div>
                          <div className="text-xs text-gray-400 mt-2">
                            {categories.find(t => t.id === message.category)?.name || selectedCategory}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>
          <div className="p-4 bg-black">
            <div className="max-w-3xl mx-auto">
              <form onSubmit={handleSendMessage} className="input-container p-2 flex items-end">
                <div className="flex items-center mr-2">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="bg-[#2a2a2a] text-gray-200 p-2 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                    disabled={isStreaming}
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>
                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 bg-transparent text-gray-100 p-2 text-base focus:outline-none resize-none min-h-[60px] max-h-[200px] placeholder-gray-500"
                  placeholder="输入您的问题..."
                  rows="2"
                  disabled={isStreaming}
                  ref={textareaRef}
                />
                {isStreaming ? (
                  <button
                    type="button"
                    onClick={stopStreaming}
                    className="bg-red-500 hover:bg-red-600 text-white font-medium p-2 rounded-md transition-colors ml-2"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                ) : (
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium p-2 rounded-md transition-colors ml-2 send-btn"
                    disabled={!input.trim()}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                  </button>
                )}
              </form>
              <div className="text-xs text-gray-500 text-center mt-2">
                按 Enter 发送，Shift + Enter 换行
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  ReactDOM.render(<App />, document.getElementById('root'));
</script>
</body>
</html>
