package com.yy.hd.service;

import com.yy.hd.chat.ranking.BgeEmbeddingSearch;
import com.yy.hd.chat.ranking.BgeRanker;
import com.yy.hd.entity.FeedbackKnowledge;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.DefaultChatClient;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class FeedbackService implements ApplicationRunner {

    @Resource
    private ChatClient chatClient;

    @Resource
    private PromptChatMemoryAdvisor promptChatMemoryAdvisor;

    @Resource
    private ToolCallingService toolCallingService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private VectorStore customerServiceVectorStore;

    @Resource
    private BgeEmbeddingSearch bgeEmbeddingSearch;

    @Resource
    private BgeRanker bgeRanker;

    private static final String TABLE_NAME = FeedbackKnowledge.TABLE;

    private static final String QUERY_SQL = "select id,category,question,content,tools from " + TABLE_NAME;

    private static final SystemMessage DEFAULT_MESSAGE = new SystemMessage("知识库没有找到相关内容，告诉用户你不知道问题的答案。");

    private static final String SYSTEM_PROMPT = """
            知识库内容：%s
            你是一个专业的客服支持助手，任务是根据客服人员提供的第三方报障信息，使用知识库的内容进行简洁的回复。
            输出内容应包括以下部分：
            1. 简要复述客服人员提供的问题，确保理解正确。
            2. 基于知识库内容和工具返回的数据（如有），生成清晰、简洁的解决方案。
            """;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        new Thread(() -> {
            List<Document> knowledgeList = jdbcTemplate.query(QUERY_SQL, new BeanPropertyRowMapper<>(FeedbackKnowledge.class))
                    .stream()
                    .map(knowledge -> {
                        String id = String.valueOf(knowledge.getId());
                        Map<String, Object> metadata = knowledge.getMetadata();
                        metadata.put(knowledge.getId().toString(), knowledge);
                        return new Document(id, knowledge.getQuestion(), metadata);
                    })
                    .toList();
            if (CollectionUtils.isNotEmpty(knowledgeList)) {
                customerServiceVectorStore.add(knowledgeList);
            }
        }).start();
    }

    public DefaultChatClient.ChatClientRequestSpec buildChatClientRequestSpec(String chatId, String question, String category) {
        FeedbackKnowledge knowledge = similaritySearch(question, category);
        if (knowledge != null) {
            String toolName = knowledge.getTools();
            List<Message> messages = new ArrayList<>();
            messages.add(new SystemMessage(SYSTEM_PROMPT.formatted(knowledge.getContent())));
            if (StringUtils.isNotEmpty(toolName)) {
                ChatResponse chatResponse = toolCallingService.decideToolCalls(question, toolName);
                List<Message> toolCallMessages = toolCallingService.executeToolCallsAccordingToDecide(question, chatResponse);
                messages.addAll(toolCallMessages);
            } else {
                messages.add(new UserMessage(question));
            }
            return chatClient.prompt()
                    .advisors(promptChatMemoryAdvisor)
                    .advisors(a -> {
                        if (StringUtils.isNotBlank(chatId)) {
                            a.param(ChatMemory.CONVERSATION_ID, chatId);
                        }
                    })
                    .messages(messages);
        }
        return chatClient.prompt()
                .messages(DEFAULT_MESSAGE);
    }

    private FeedbackKnowledge similaritySearch(String question, String category) {
        String textExpression = null;
        if (StringUtils.isNotBlank(category) && !StringUtils.equalsIgnoreCase(category, "none")) {
            textExpression = "category == '%s'".formatted(category);
        }
        List<Document> documents = bgeEmbeddingSearch.similaritySearch(customerServiceVectorStore,
                question, 0.5, 10, textExpression);
        if (CollectionUtils.isEmpty(documents)) {
            return null;
        }
        Optional<Document> optionalDocument = bgeRanker.reranking(documents, question)
                .stream()
                .filter(document -> document.getScore() > 0.0D) // 必须要正相关
                .findFirst();
        if (optionalDocument.isEmpty()) {
            return null;
        }
        Document document = optionalDocument.get();
        String id = document.getId();
        return  (FeedbackKnowledge) document.getMetadata().get(id);
    }

    public List<FeedbackCategoryType> categories() {
        return Arrays.asList(FeedbackCategoryType.values());
    }

    @Getter
    public enum FeedbackCategoryType {
        NONE("none", "选择标签"),
        ROOM("room", "房间"),
        GIFT("gift", "礼物"),
        PLAY("play", "玩法"),
        ROOM_CHAT("room_chat", "公屏消息"),
        IM("im", "im消息"),
        CLOUDGAME("cloudgame", "云游戏"),
        MASTER("master", "大神"),
        ACTIVITY("activity", "活动");

        private final String id;
        private final String name;

        FeedbackCategoryType(String id, String name) {
            this.id = id;
            this.name = name;
        }

    }
}
