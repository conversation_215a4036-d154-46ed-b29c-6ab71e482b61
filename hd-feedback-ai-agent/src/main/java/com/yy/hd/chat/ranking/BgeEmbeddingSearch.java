package com.yy.hd.chat.ranking;

import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 粗排：向量相似度检索
 */
@Service
public class BgeEmbeddingSearch {

    private static final String INSTRUCTION = "为这个句子生成表示以用于检索相关文章";

    public List<Document> similaritySearch(VectorStore vectorStore, String query, double similarity, int topK, String textExpression) {
        return vectorStore.similaritySearch(
                SearchRequest.builder()
                        .query(INSTRUCTION + query)
                        .similarityThreshold(similarity)
                        .filterExpression(textExpression)
                        .topK(topK)
                        .build()
        );
    }
}
