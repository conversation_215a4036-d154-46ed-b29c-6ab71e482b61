package com.yy.hd.controller;

import com.yy.hd.chat.ChatReq;
import com.yy.hd.service.FeedbackService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.DefaultChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import utils.JsonUtils;

@Slf4j
@AllArgsConstructor
@RestController
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;

    @GetMapping(value = "/api/categories")
    public Flux<FeedbackService.Category> categories() {
        return Flux.fromIterable(feedbackService.categories());
    }

    @PostMapping(value = "/stream/feedback", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> feedback(@RequestBody ChatReq req) {
        log.info("feedback req, body:{}", JsonUtils.toJson(req));
        DefaultChatClient.ChatClientRequestSpec chatClientRequestSpec = feedbackService.buildChatClientRequestSpec(req.getChatId(), req.getMessage(), req.getCategory());
        Flux<String> output = chatClientRequestSpec
                .stream()
                .content();
        // 映射为SSE格式，添加[DONE]标记
        return output.concatWith(Flux.just("[DONE]"));
    }
}
