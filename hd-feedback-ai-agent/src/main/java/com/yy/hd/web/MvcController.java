package com.yy.hd.web;

import com.yy.hd.chat.ChatReq;
import com.yy.hd.service.FeedbackService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.DefaultChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import utils.JsonUtils;

@Slf4j
@AllArgsConstructor
@RequestMapping("/mvc")
@RestController
public class MvcController {

    @Resource
    private FeedbackService feedbackService;

    @GetMapping(value = "/categories")
    public Flux<FeedbackService.Category> categories(@RequestHeader(HttpHeaderKeys.UID_HEADER) Long uid) {
        log.info("categories req, uid:{}", uid);
        return Flux.fromIterable(feedbackService.categories());
    }

    @PostMapping(value = "/stream/feedback", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> feedback(@RequestHeader(HttpHeaderKeys.UID_HEADER) Long uid, @RequestBody ChatReq req) {
        log.info("feedback req, uid:{}, body:{}", uid, JsonUtils.toJson(req));
        DefaultChatClient.ChatClientRequestSpec chatClientRequestSpec = feedbackService.buildChatClientRequestSpec(uid, req.getChatId(), req.getMessage(), req.getCategory());
        Flux<String> output = chatClientRequestSpec
                .stream()
                .content();
        // 映射为SSE格式，添加[DONE]标记
        return output.concatWith(Flux.just("[DONE]"));
    }

}
