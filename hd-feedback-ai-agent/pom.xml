<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yy.hd</groupId>
        <artifactId>hd-ai-agent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hd-feedback-ai-agent</artifactId>

    <properties>
        <mysql-connector-j.version>9.0.0</mysql-connector-j.version>
        <reflections.version>0.10.2</reflections.version>
        <jsoup.version>1.19.1</jsoup.version>
        <udb.version>2.0.0</udb.version>
        <shenshu.version>2.0.3-SNAPSHOT</shenshu.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-chat-memory-repository-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>spring-cloud-starter-hd-s2s-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.vincenzoracca</groupId>
            <artifactId>spring-webflux-mdc</artifactId>
            <version>1.0.2</version>
        </dependency>

        <!--<dependency>
            <groupId>com.baidu.yy</groupId>
            <artifactId>shenshu-spring-boot-starter</artifactId>
            <version>${shenshu.version}</version>
        </dependency>-->

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>${reflections.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.huanju.udb.lgn</groupId>
            <artifactId>udb-client-intranet</artifactId>
            <version>${udb.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yy.hd</groupId>
            <artifactId>hd-ai-agent-ranking</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>hd-feedback-ai-agent</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin><!-- 构造MANIFEST.MF文件 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix><!-- 潜龙将使用此目录作为启动的classpath参数 -->
                            <mainClass>com.yy.hd.FeedbackAgentApplication</mainClass><!-- 此处根据项目的Main类进行修改 -->
                            <useUniqueVersions>false</useUniqueVersions><!-- 解决snapshot的jar文件名与MANIFEST.MF文件中不一致的问题 -->
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies-runtime</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/classes/lib</outputDirectory>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-dependencies-system</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/classes/lib</outputDirectory>
                            <includeScope>system</includeScope>
                            <excludeTransitive>true</excludeTransitive>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <!-- 排除二进制文件过滤 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>onnx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>